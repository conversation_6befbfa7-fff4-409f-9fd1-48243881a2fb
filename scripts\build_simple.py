#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名邮箱管理器 - 简化的PyInstaller构建脚本
使用正确的PyInstaller方法创建单文件可执行程序

作者: 自动邮箱管理器项目组
创建时间: 2025年1月
"""

import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path

# 项目配置
PROJECT_NAME = "EmailDomainManager"
VERSION = "1.0.0"

# 路径配置
ROOT_DIR = Path(__file__).parent.parent
SRC_DIR = ROOT_DIR / "src"
BUILD_DIR = ROOT_DIR / "build"
DIST_DIR = ROOT_DIR / "dist"
RESOURCES_DIR = SRC_DIR / "resources"


def print_step(message):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"🔧 {message}")
    print('='*60)


def clean_build_dirs():
    """清理构建目录"""
    print_step("清理构建目录")
    
    for dir_path in [BUILD_DIR, DIST_DIR]:
        if dir_path.exists():
            print(f"🗑️ 删除目录: {dir_path}")
            shutil.rmtree(dir_path)
        else:
            print(f"📁 目录不存在: {dir_path}")
    
    # 创建必要的目录
    BUILD_DIR.mkdir(exist_ok=True)
    DIST_DIR.mkdir(exist_ok=True)
    
    print("✅ 构建目录清理完成")


def build_windows():
    """构建Windows版本"""
    print_step("构建Windows版本")
    
    main_script = SRC_DIR / "main.py"
    if not main_script.exists():
        print(f"❌ 主脚本不存在: {main_script}")
        return False
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 创建单个可执行文件
        "--windowed",                   # 隐藏控制台窗口
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        f"--name={PROJECT_NAME}",       # 可执行文件名称
        f"--distpath={DIST_DIR / 'windows'}",  # 输出目录
        f"--workpath={BUILD_DIR}",      # 工作目录
    ]
    
    # 添加图标
    icon_file = RESOURCES_DIR / "icons" / "app.ico"
    if icon_file.exists():
        cmd.append(f"--icon={icon_file}")
        print(f"🎨 使用图标: {icon_file}")
    else:
        print("⚠️ 未找到Windows图标文件")
    
    # 添加资源文件
    if RESOURCES_DIR.exists():
        cmd.append(f"--add-data={RESOURCES_DIR}{os.pathsep}resources")
        print(f"📁 添加资源目录: {RESOURCES_DIR}")
    
    # 添加隐藏导入
    hidden_imports = [
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "PyQt6.QtQml",
        "PyQt6.QtQuick",
        "PyQt6.QtQuickControls2",
        "asyncio",
        "asyncqt",
        "cryptography",
        "sqlite3",
    ]
    
    for module in hidden_imports:
        cmd.append(f"--hidden-import={module}")
    
    # 添加主脚本
    cmd.append(str(main_script))
    
    print(f"🔨 执行命令:")
    print(f"   {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=ROOT_DIR, check=True, capture_output=True, text=True)
        
        # 检查生成的可执行文件
        exe_path = DIST_DIR / "windows" / f"{PROJECT_NAME}.exe"
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ Windows构建成功!")
            print(f"📦 可执行文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.1f} MB")
            return True
        else:
            print(f"❌ 可执行文件未找到: {exe_path}")
            return False
            
    except subprocess.CalledProcessError as e:
        print("❌ Windows构建失败!")
        print(f"错误输出: {e.stderr}")
        return False


def build_linux():
    """构建Linux版本"""
    print_step("构建Linux版本")
    
    main_script = SRC_DIR / "main.py"
    if not main_script.exists():
        print(f"❌ 主脚本不存在: {main_script}")
        return False
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 创建单个可执行文件
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        f"--name={PROJECT_NAME}",       # 可执行文件名称
        f"--distpath={DIST_DIR / 'linux'}",   # 输出目录
        f"--workpath={BUILD_DIR}",      # 工作目录
    ]
    
    # 添加图标
    icon_file = RESOURCES_DIR / "icons" / "app.png"
    if icon_file.exists():
        cmd.append(f"--icon={icon_file}")
        print(f"🎨 使用图标: {icon_file}")
    else:
        print("⚠️ 未找到Linux图标文件")
    
    # 添加资源文件
    if RESOURCES_DIR.exists():
        cmd.append(f"--add-data={RESOURCES_DIR}{os.pathsep}resources")
        print(f"📁 添加资源目录: {RESOURCES_DIR}")
    
    # 添加隐藏导入
    hidden_imports = [
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "PyQt6.QtQml",
        "PyQt6.QtQuick",
        "PyQt6.QtQuickControls2",
        "asyncio",
        "asyncqt",
        "cryptography",
        "sqlite3",
    ]
    
    for module in hidden_imports:
        cmd.append(f"--hidden-import={module}")
    
    # 添加主脚本
    cmd.append(str(main_script))
    
    print(f"🔨 执行命令:")
    print(f"   {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=ROOT_DIR, check=True, capture_output=True, text=True)
        
        # 检查生成的可执行文件
        exe_path = DIST_DIR / "linux" / PROJECT_NAME
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ Linux构建成功!")
            print(f"📦 可执行文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.1f} MB")
            return True
        else:
            print(f"❌ 可执行文件未找到: {exe_path}")
            return False
            
    except subprocess.CalledProcessError as e:
        print("❌ Linux构建失败!")
        print(f"错误输出: {e.stderr}")
        return False


def main():
    """主函数"""
    print_step(f"开始构建 {PROJECT_NAME} v{VERSION}")
    
    # 检查PyInstaller是否安装
    try:
        subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                      check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("❌ PyInstaller未安装，请运行: pip install pyinstaller")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 根据当前平台构建
    current_platform = platform.system().lower()
    success = False
    
    if current_platform == "windows":
        success = build_windows()
    elif current_platform == "linux":
        success = build_linux()
    else:
        print(f"❌ 不支持的平台: {current_platform}")
        return False
    
    if success:
        print_step("构建完成!")
        print(f"✅ {PROJECT_NAME} 构建成功")
        print(f"📁 输出目录: {DIST_DIR}")
    else:
        print_step("构建失败!")
        print(f"❌ {PROJECT_NAME} 构建失败")
    
    return success


if __name__ == "__main__":
    # 解析命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--windows":
            clean_build_dirs()
            success = build_windows()
        elif sys.argv[1] == "--linux":
            clean_build_dirs()
            success = build_linux()
        elif sys.argv[1] == "--help":
            print("用法:")
            print("  python build_simple.py          # 自动检测平台并构建")
            print("  python build_simple.py --windows # 构建Windows版本")
            print("  python build_simple.py --linux   # 构建Linux版本")
            sys.exit(0)
        else:
            print(f"未知参数: {sys.argv[1]}")
            sys.exit(1)
    else:
        success = main()
    
    sys.exit(0 if success else 1)
